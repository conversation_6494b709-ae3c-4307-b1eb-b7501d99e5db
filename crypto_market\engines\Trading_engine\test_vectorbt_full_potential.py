"""
Test VectorBT Full Potential Implementation
==========================================

Test script to demonstrate all advanced VectorBT features for big trend trading.
This script showcases the complete power of VectorBT integration.
"""

import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Import our enhanced VectorBT system
from backtester_vbt import (
    BigTrendVectorBTBacktester,
    run_big_trend_backtest,
    run_comprehensive_analysis
)

def generate_sample_data(days=60):
    """Generate sample BTC-like data for testing"""
    
    print("📊 Generating sample BTC data for testing...")
    
    # Generate 3-minute data
    periods_3m = days * 24 * 20  # 20 periods per hour for 3-minute data
    dates_3m = pd.date_range(
        start=datetime.now() - timedelta(days=days),
        periods=periods_3m,
        freq='3min'
    )
    
    # Generate realistic BTC price movement
    np.random.seed(42)
    base_price = 50000
    
    # Create trending price with volatility
    trend = np.linspace(0, 0.2, periods_3m)  # 20% uptrend over period
    volatility = np.random.normal(0, 0.002, periods_3m)  # 0.2% volatility
    price_changes = trend + volatility
    
    prices = base_price * np.exp(np.cumsum(price_changes))
    
    # Generate OHLCV data
    df_3m = pd.DataFrame({
        'timestamp': dates_3m,
        'open': prices + np.random.normal(0, 50, periods_3m),
        'high': prices + np.abs(np.random.normal(100, 50, periods_3m)),
        'low': prices - np.abs(np.random.normal(100, 50, periods_3m)),
        'close': prices,
        'volume': np.random.uniform(100, 1000, periods_3m)
    })
    df_3m.set_index('timestamp', inplace=True)
    
    # Generate 15-minute data (every 5th 3-minute candle)
    df_15m = df_3m.iloc[::5].copy()
    
    # Generate 1-hour data (every 20th 3-minute candle)
    df_1h = df_3m.iloc[::20].copy()
    
    print(f"✅ Generated sample data:")
    print(f"   3m: {len(df_3m)} candles")
    print(f"   15m: {len(df_15m)} candles")
    print(f"   1h: {len(df_1h)} candles")
    print(f"   Price range: ${df_3m['close'].min():,.0f} - ${df_3m['close'].max():,.0f}")
    
    return df_3m, df_15m, df_1h

def test_basic_backtest():
    """Test basic VectorBT backtest"""
    
    print("\n" + "="*60)
    print("🧪 TESTING BASIC VECTORBT BACKTEST")
    print("="*60)
    
    # Generate test data
    df_3m, df_15m, df_1h = generate_sample_data(days=30)
    
    # Run basic backtest
    results = run_big_trend_backtest(df_3m, df_15m, df_1h, initial_capital=100000, analysis_type='basic')
    
    if results and results.get('basic'):
        basic_results = results['basic']
        print(f"\n✅ Basic backtest completed successfully!")
        print(f"   Total Return: {basic_results.get('total_return', 0):.2%}")
        print(f"   Total Trades: {basic_results.get('total_trades', 0)}")
        print(f"   Win Rate: {basic_results.get('win_rate', 0):.1%}")
        print(f"   Max Drawdown: {basic_results.get('max_drawdown', 0):.2%}")
        return True
    else:
        print("❌ Basic backtest failed")
        return False

def test_parameter_optimization():
    """Test parameter optimization"""
    
    print("\n" + "="*60)
    print("🧪 TESTING PARAMETER OPTIMIZATION")
    print("="*60)
    
    # Generate test data
    df_3m, df_15m, df_1h = generate_sample_data(days=45)
    
    # Create backtester
    backtester = BigTrendVectorBTBacktester(100000)
    
    # Run optimization with limited parameters for testing
    param_ranges = {
        'min_confluence': [3, 4],
        'min_rr': [3.0, 4.0],
        'max_sl_points': [250, 300]
    }
    
    results = backtester.run_parameter_optimization(
        df_3m, df_15m, df_1h, 
        param_ranges=param_ranges,
        optimization_metric='sharpe_ratio'
    )
    
    if results and results.get('best_params'):
        print(f"\n✅ Parameter optimization completed!")
        print(f"   Best Score: {results.get('best_score', 0):.4f}")
        print(f"   Best Parameters: {results.get('best_params', {})}")
        print(f"   Tested {len(results.get('all_results', []))} combinations")
        return True
    else:
        print("❌ Parameter optimization failed")
        return False

def test_monte_carlo_simulation():
    """Test Monte Carlo simulation"""
    
    print("\n" + "="*60)
    print("🧪 TESTING MONTE CARLO SIMULATION")
    print("="*60)
    
    # Generate test data
    df_3m, df_15m, df_1h = generate_sample_data(days=40)
    
    # Create backtester
    backtester = BigTrendVectorBTBacktester(100000)
    
    # Run Monte Carlo with fewer simulations for testing
    results = backtester.run_monte_carlo_simulation(
        df_3m, df_15m, df_1h, 
        num_simulations=100
    )
    
    if results:
        print(f"\n✅ Monte Carlo simulation completed!")
        print(f"   Expected Return: {results.get('expected_return', 0):.2%}")
        print(f"   5% VaR: {results.get('var_5', 0):.2%}")
        print(f"   Probability of Profit: {results.get('probability_of_profit', 0):.1%}")
        print(f"   Simulations: {results.get('simulations', 0)}")
        return True
    else:
        print("❌ Monte Carlo simulation failed")
        return False

def test_dynamic_position_sizing():
    """Test dynamic position sizing"""
    
    print("\n" + "="*60)
    print("🧪 TESTING DYNAMIC POSITION SIZING")
    print("="*60)
    
    # Generate test data
    df_3m, df_15m, df_1h = generate_sample_data(days=35)
    
    # Create backtester
    backtester = BigTrendVectorBTBacktester(100000)
    
    # Test different sizing methods
    sizing_methods = ['kelly', 'volatility', 'fixed_risk']
    results = {}
    
    for method in sizing_methods:
        print(f"\n   Testing {method} sizing...")
        result = backtester.run_dynamic_position_sizing(
            df_3m, df_15m, df_1h, 
            sizing_method=method,
            risk_target=0.02
        )
        
        if result:
            results[method] = result
            print(f"     ✅ {method}: {result.get('total_return', 0):.2%} return")
        else:
            print(f"     ❌ {method} failed")
    
    if results:
        print(f"\n✅ Dynamic position sizing completed!")
        print(f"   Tested {len(results)} sizing methods")
        return True
    else:
        print("❌ Dynamic position sizing failed")
        return False

def test_comprehensive_analysis():
    """Test comprehensive analysis with all features"""
    
    print("\n" + "="*60)
    print("🧪 TESTING COMPREHENSIVE ANALYSIS")
    print("="*60)
    
    # Generate test data
    df_3m, df_15m, df_1h = generate_sample_data(days=50)
    
    # Run comprehensive analysis (limited for testing)
    results = run_comprehensive_analysis(
        df_3m, df_15m, df_1h, 
        initial_capital=100000, 
        analysis_type='optimization'  # Test optimization only for speed
    )
    
    if results:
        print(f"\n✅ Comprehensive analysis completed!")
        print(f"   Analysis components: {list(results.keys())}")
        return True
    else:
        print("❌ Comprehensive analysis failed")
        return False

def main():
    """Run all VectorBT tests"""
    
    print("🚀 TESTING VECTORBT FULL POTENTIAL IMPLEMENTATION")
    print("="*80)
    print("Testing all advanced VectorBT features for big trend trading...")
    
    test_results = {}
    
    # Run all tests
    test_results['basic'] = test_basic_backtest()
    test_results['optimization'] = test_parameter_optimization()
    test_results['monte_carlo'] = test_monte_carlo_simulation()
    test_results['sizing'] = test_dynamic_position_sizing()
    test_results['comprehensive'] = test_comprehensive_analysis()
    
    # Print final results
    print("\n" + "="*80)
    print("🏁 FINAL TEST RESULTS")
    print("="*80)
    
    passed_tests = sum(test_results.values())
    total_tests = len(test_results)
    
    for test_name, passed in test_results.items():
        status = "✅ PASSED" if passed else "❌ FAILED"
        print(f"   {test_name.upper()}: {status}")
    
    print(f"\nOVERALL: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        print("🎉 ALL TESTS PASSED! VectorBT full potential is working!")
    else:
        print("⚠️ Some tests failed. Check the implementation.")
    
    return passed_tests == total_tests

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
