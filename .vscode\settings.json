{"python.analysis.extraPaths": ["./crypto_market/engines/Trading_engine", "./crypto_market/engines", "./crypto_market", "./core", "./dashboard"], "python.analysis.autoSearchPaths": true, "python.analysis.autoImportCompletions": true, "python.analysis.typeCheckingMode": "basic", "python.linting.enabled": true, "python.linting.pylintEnabled": false, "python.linting.flake8Enabled": false, "python.analysis.diagnosticMode": "workspace", "python.analysis.stubPath": "./typings", "python.analysis.include": ["dashboard", "crypto_market", "core"], "pylance.insidersChannel": "off"}