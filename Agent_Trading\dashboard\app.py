#!/usr/bin/env python3
"""
Simple Trading Dashboard - Clean and Reliable
No complex caching, no force reload, just works!
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from pathlib import Path
import sys

# Setup paths - Use absolute paths to avoid issues
dashboard_dir = Path(__file__).parent.absolute()
agent_trading_dir = dashboard_dir.parent.absolute()
root_dir = agent_trading_dir.parent.absolute()

# Add paths to sys.path
sys.path.insert(0, str(root_dir))
sys.path.insert(0, str(agent_trading_dir))

# Page configuration
st.set_page_config(
    page_title="Professional BTC Trading System",
    page_icon="📈",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Simple CSS
st.markdown("""
<style>
.main-header {
    color: #1f77b4;
    text-align: center;
    padding: 1rem 0;
    border-bottom: 2px solid #1f77b4;
    margin-bottom: 2rem;
}
</style>
""", unsafe_allow_html=True)

class SimpleTradingDashboard:
    """Simple, reliable trading dashboard"""

    def __init__(self):
        """Initialize dashboard"""
        self.load_components()

    def load_components(self):
        """Load trading system components - simple and reliable"""
        try:
            # Import required modules
            from core.config_manager import ConfigManager
            from crypto_market.engines.data_engine.simple_data_fetcher import SimpleDataFetcher
            from crypto_market.engines.data_engine.database_manager import DatabaseManager

            # Simple config path
            config_path = agent_trading_dir / "config.json"

            if not config_path.exists():
                st.error(f"❌ Config file not found at: {config_path}")
                st.stop()

            # Load components
            self.config = ConfigManager(str(config_path))
            self.database_manager = DatabaseManager(self.config)
            self.data_fetcher = SimpleDataFetcher(self.config)

            # Simple success message
            st.success("✅ Trading system ready!")

        except Exception as e:
            st.error(f"❌ Failed to load trading system: {e}")
            st.info("💡 Please check your configuration and try refreshing the page")
            st.stop()

    def render_sidebar(self):
        """Render sidebar navigation"""
        st.sidebar.markdown("## 🎯 **Professional BTC Trading**")
        st.sidebar.success("✅ **System Ready**")

        st.sidebar.markdown("### Navigation")
        page = st.sidebar.selectbox(
            "Select Page",
            ["📊 Dashboard", "📈 Data Management", "🚀 Backtesting", "🎯 Signal Analysis"],
            key="main_navigation"
        )

        st.sidebar.markdown("---")
        st.sidebar.markdown("### 📊 **System Info**")
        st.sidebar.info("🎯 Target: 300-3000 points")
        st.sidebar.info("⚡ Frequency: 2-3 trades/day")

        return page

    def render_dashboard_page(self):
        """Render main dashboard"""
        st.markdown('<h1 class="main-header">📈 Professional BTC Trading System</h1>', unsafe_allow_html=True)

        # System status
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("🔧 System Status", "Live")
        with col2:
            st.metric("🎯 Target Points", "300-3000")
        with col3:
            st.metric("📊 Signal Quality", "75%+")
        with col4:
            st.metric("⚡ Trade Frequency", "2-3/day")

        st.markdown("---")

        # Main content
        col1, col2 = st.columns([2, 1])
        with col1:
            st.subheader("📊 Market Overview")
            self.render_market_chart()
        with col2:
            st.subheader("🎯 Latest Signals")
            self.render_signals()

    def render_data_management_page(self):
        """Render data management page"""
        st.header("📈 Professional Data Management")

        # Single database policy notice
        st.info("🎯 **Single Database Policy**: We use only ONE database location: `crypto_market/data/crypto_trading.db`")

        # Database Status with auto-refresh
        st.subheader("🗄️ Database Status")

        # Create a container for database info that can be updated
        db_container = st.container()

        with db_container:
            try:
                db_info = self.database_manager.get_database_info()

                col1, col2, col3 = st.columns(3)
                with col1:
                    st.metric("📦 Database Status", "✅ Ready" if db_info['database_exists'] else "❌ Missing")
                    st.metric("💾 Database Size", f"{db_info['database_size_mb']:.2f} MB")
                with col2:
                    st.metric("📈 Total Records", f"{db_info['total_records']:,}")
                with col3:
                    if db_info['total_records'] > 0:
                        st.write("**Records by Timeframe:**")
                        for timeframe, count in db_info['timeframes'].items():
                            st.write(f"🕐 {timeframe}: {count:,}")
                    else:
                        st.write("**No data available**")

            except Exception as e:
                st.error(f"❌ Database error: {e}")

        st.markdown("---")

        # Data Fetching
        st.subheader("📥 Data Fetching")

        col1, col2 = st.columns(2)
        with col1:
            st.write("**📅 Select Date Range:**")
            from datetime import datetime, timedelta
            default_end = datetime.now().date()
            default_start = default_end - timedelta(days=30)

            start_date = st.date_input("Start Date", value=default_start, key="start_date")
            end_date = st.date_input("End Date", value=default_end, key="end_date")
            days_to_fetch = (end_date - start_date).days + 1
            st.info(f"📊 Will fetch {days_to_fetch} days of data")

        with col2:
            st.write("**⚙️ Fetch Options:**")
            force_refresh = st.checkbox("🔄 Force Refresh", value=False)
            timeframes = st.multiselect(
                "🕐 Timeframes",
                options=["3m", "15m", "1h"],
                default=["3m", "15m", "1h"]
            )

        # Action buttons
        st.markdown("**🚀 Actions:**")
        col1, col2, col3 = st.columns(3)

        with col1:
            if st.button("📥 Fetch Selected Data", disabled=not timeframes):
                if timeframes:
                    self.fetch_data(start_date, end_date, timeframes, force_refresh)

        with col2:
            if st.button("📊 Quick Fetch (7 days)"):
                quick_start = default_end - timedelta(days=7)
                self.fetch_data(quick_start, default_end, ["3m", "15m", "1h"], False)

        with col3:
            if st.button("🗑️ Clear Database"):
                self.clear_database()

        # SQL Commands section
        st.markdown("---")
        st.subheader("⚡ SQL Commands")

        # Quick command buttons
        col1, col2, col3 = st.columns(3)
        with col1:
            if st.button("📊 Count Records", key="count_records"):
                st.session_state.sql_command = "SELECT COUNT(*) FROM market_data"
        with col2:
            if st.button("🗑️ SQL Clear All", key="sql_clear"):
                st.session_state.sql_command = "DELETE FROM market_data; DELETE FROM indicators; DELETE FROM signals; DELETE FROM trades; DELETE FROM performance_metrics; DELETE FROM sqlite_sequence;"
        with col3:
            if st.button("📋 Show Tables", key="show_tables"):
                st.session_state.sql_command = "SELECT name FROM sqlite_master WHERE type='table'"

        # SQL input
        sql_command = st.text_area(
            "SQL Command:",
            value=st.session_state.get('sql_command', ''),
            height=100,
            key="sql_input"
        )
        st.session_state.sql_command = sql_command

        # Execute SQL button
        if st.button("⚡ Execute SQL", key="execute_sql"):
            if sql_command.strip():
                try:
                    import sqlite3
                    db_path = self.database_manager.get_absolute_database_path()

                    with sqlite3.connect(db_path) as conn:
                        cursor = conn.execute(sql_command)
                        rows_affected = cursor.rowcount
                        conn.commit()

                        st.success(f"✅ SQL executed successfully! Rows affected: {rows_affected}")

                        # Show updated count
                        cursor = conn.execute("SELECT COUNT(*) FROM market_data")
                        count = cursor.fetchone()[0]
                        st.info(f"📊 Current market_data records: {count}")

                except Exception as e:
                    st.error(f"❌ SQL execution error: {e}")



        # Data visualization
        st.markdown("---")
        st.subheader("📊 Data Visualization")
        self.render_data_chart()

    def fetch_data(self, start_date, end_date, timeframes, force_refresh):
        """Enhanced data fetching with progress tracking"""
        from datetime import datetime

        start_dt = datetime.combine(start_date, datetime.min.time())
        end_dt = datetime.combine(end_date, datetime.max.time())

        # Create progress tracking
        progress_bar = st.progress(0)
        status_text = st.empty()

        try:
            status_text.text("🚀 Starting data fetch...")
            progress_bar.progress(10)

            success = self.data_fetcher.fetch_custom_range(
                start_date=start_dt,
                end_date=end_dt,
                timeframes=timeframes,
                force_refresh=force_refresh
            )

            progress_bar.progress(100)

            if success:
                status_text.empty()
                progress_bar.empty()
                st.success("✅ Data fetched successfully!")
                # Auto-refresh the database info
                st.rerun()
            else:
                status_text.empty()
                progress_bar.empty()
                st.warning("⚠️ Data fetch partially completed")

        except Exception as e:
            status_text.empty()
            progress_bar.empty()
            st.error(f"❌ Data fetch failed: {e}")

    def clear_database(self):
        """Simple database clearing - works exactly like SQL command"""
        st.warning("⚠️ **WARNING**: This will permanently delete all data!")

        if st.button("🗑️ **CONFIRM CLEAR ALL DATA**", key="clear_all_data", type="primary"):
            try:
                import sqlite3
                db_path = self.database_manager.get_absolute_database_path()

                with st.spinner("🗑️ Clearing database..."):
                    # Use direct SQLite connection like the SQL command
                    with sqlite3.connect(db_path) as conn:
                        # Get count before clearing
                        cursor = conn.execute("SELECT COUNT(*) FROM market_data")
                        before_count = cursor.fetchone()[0]

                        # Clear all tables (same as SQL command)
                        conn.execute("DELETE FROM market_data")
                        conn.execute("DELETE FROM indicators")
                        conn.execute("DELETE FROM signals")
                        conn.execute("DELETE FROM trades")
                        conn.execute("DELETE FROM performance_metrics")
                        conn.execute("DELETE FROM sqlite_sequence")
                        conn.commit()

                        # Get count after clearing
                        cursor = conn.execute("SELECT COUNT(*) FROM market_data")
                        after_count = cursor.fetchone()[0]

                        st.success(f"✅ Database cleared successfully!")
                        st.info(f"📊 Deleted {before_count:,} records. Current records: {after_count}")
                        st.balloons()

                        # Refresh the page to show updated counts
                        st.rerun()

            except Exception as e:
                st.error(f"❌ Clear failed: {e}")
                import traceback
                st.code(traceback.format_exc())

    def render_market_chart(self):
        """Simple market chart"""
        dates = pd.date_range(start='2024-01-01', periods=100, freq='D')
        prices = 50000 + np.cumsum(np.random.randn(100) * 500)

        fig = go.Figure()
        fig.add_trace(go.Scatter(x=dates, y=prices, mode='lines', name='BTC Price'))
        fig.update_layout(title="BTC Price Chart", height=400)
        st.plotly_chart(fig, use_container_width=True)

    def render_signals(self):
        """Simple signals display"""
        signals = [
            {"time": "10:30", "type": "BUY", "strength": 85},
            {"time": "14:15", "type": "SELL", "strength": 72},
            {"time": "16:45", "type": "BUY", "strength": 90}
        ]

        for signal in signals:
            color = "🟢" if signal['type'] == 'BUY' else "🔴"
            st.write(f"{color} {signal['time']} - {signal['type']} (Strength: {signal['strength']}%)")

    def render_data_chart(self):
        """Enhanced data chart with professional features"""
        col1, col2 = st.columns([3, 1])

        with col2:
            st.markdown("**📊 Chart Settings**")
            timeframe = st.selectbox("Timeframe", ["3m", "15m", "1h"], index=2, key="chart_timeframe")
            limit = st.selectbox("Records", [50, 100, 200, 500], index=1, key="chart_limit")

            # Chart type selection
            chart_type = st.radio("Chart Type", ["Candlestick", "Line"], key="chart_type")

            # Auto-refresh toggle
            auto_refresh = st.checkbox("Auto Refresh", value=False, key="auto_refresh")

            if auto_refresh:
                st.info("🔄 Auto-refresh enabled")

        with col1:
            try:
                df = self.database_manager.get_market_data(timeframe=timeframe, limit=limit)

                if df.empty:
                    st.info(f"📊 No {timeframe} data available. Please fetch data first.")
                    return

                # Create chart based on selection
                fig = go.Figure()

                if chart_type == "Candlestick":
                    fig.add_trace(go.Candlestick(
                        x=df.index,
                        open=df['open'],
                        high=df['high'],
                        low=df['low'],
                        close=df['close'],
                        name="BTC/USD"
                    ))
                else:
                    fig.add_trace(go.Scatter(
                        x=df.index,
                        y=df['close'],
                        mode='lines',
                        name='BTC/USD Close',
                        line=dict(color='#1f77b4', width=2)
                    ))

                # Enhanced layout
                fig.update_layout(
                    title=f"BTC/USD ({timeframe}) - {len(df)} candles",
                    height=500,
                    xaxis_rangeslider_visible=False,
                    showlegend=True,
                    xaxis_title="Time",
                    yaxis_title="Price (USD)",
                    template="plotly_white"
                )

                st.plotly_chart(fig, use_container_width=True)

                # Show latest price info
                if not df.empty:
                    latest = df.iloc[-1]
                    col_a, col_b, col_c, col_d = st.columns(4)
                    with col_a:
                        st.metric("Latest Close", f"${latest['close']:,.2f}")
                    with col_b:
                        st.metric("24h High", f"${df['high'].tail(24).max():,.2f}")
                    with col_c:
                        st.metric("24h Low", f"${df['low'].tail(24).min():,.2f}")
                    with col_d:
                        st.metric("Volume", f"{latest['volume']:,.0f}")

            except Exception as e:
                st.error(f"❌ Error loading chart: {e}")



    def render_backtesting_page(self):
        """Simple and Clean Backtesting Interface"""
        st.header("🚀 Big Trend Strategy Backtesting")

        # Strategy overview
        st.info("🎯 **Strategy**: Capture 800-3000+ point BTC moves with 200-300 point stops, 1:4+ R:R, max 3 trades/day")

        # Simple Analysis Type Selection
        st.subheader("📊 Backtest Type")

        analysis_type = st.selectbox(
            "Choose Backtest Type:",
            [
                "📊 Basic Backtest (Recommended)",
                "🔧 Parameter Optimization (Find Best Settings)",
                "📈 Walk-Forward Validation (Test Robustness)"
            ],
            key="analysis_type"
        )

        # Simple descriptions
        if "Basic Backtest" in analysis_type:
            st.info("📊 **Basic Backtest**: Test your strategy with current settings")
        elif "Parameter Optimization" in analysis_type:
            st.info("🔧 **Parameter Optimization**: Automatically find the best strategy settings")
        elif "Walk-Forward" in analysis_type:
            st.info("📈 **Walk-Forward**: Test if strategy works on future unseen data (prevents overfitting)")

        # Backtest configuration
        st.subheader("⚙️ Backtest Configuration")

        col1, col2 = st.columns(2)

        with col1:
            st.markdown("**📅 Data Selection:**")

            # Data period selection
            period_type = st.radio(
                "Select Data Period:",
                ["Recent Days", "Custom Date Range", "All Available Data"],
                key="period_type"
            )

            if period_type == "Recent Days":
                days = st.slider("Number of Days", min_value=7, max_value=365, value=60, key="backtest_days")
                st.info(f"📊 Will test on last {days} days of data")

            elif period_type == "Custom Date Range":
                from datetime import datetime, timedelta
                default_end = datetime.now().date()
                default_start = default_end - timedelta(days=90)

                start_date = st.date_input("Start Date", value=default_start, key="bt_start_date")
                end_date = st.date_input("End Date", value=default_end, key="bt_end_date")

                if start_date >= end_date:
                    st.error("❌ Start date must be before end date")
                else:
                    days_span = (end_date - start_date).days
                    st.info(f"📊 Will test {days_span} days from {start_date} to {end_date}")

            else:  # All Available Data
                st.info("📊 Will test on all available data (286K+ records)")

        with col2:
            st.markdown("**💰 Trading Parameters:**")

            initial_capital = st.number_input(
                "Initial Capital ($)",
                min_value=10000,
                max_value=1000000,
                value=100000,
                step=10000,
                key="initial_capital"
            )

            # Strategy settings (only for basic backtest)
            if "Basic Enhanced" in analysis_type:
                st.markdown("**🎯 Strategy Settings:**")
                min_confluence = st.slider("Min Confluence Signals", 3, 6, 4, key="min_confluence")
                min_rr = st.slider("Min Risk:Reward", 2.0, 8.0, 4.0, 0.5, key="min_rr")
                max_sl_points = st.slider("Max Stop Loss (points)", 150, 500, 300, 25, key="max_sl_points")
                st.info(f"🎯 Strategy: {min_confluence}+ signals, {min_rr}:1 R:R, {max_sl_points}pt stops")
            else:
                st.info("🔧 **Advanced Analysis**: Parameters will be optimized automatically")

        # Advanced settings for specific analysis types
        if "Parameter Optimization" in analysis_type:
            st.subheader("🔧 Optimization Settings")
            st.info("🎯 Will automatically test different parameter combinations to find the best settings")

        elif "Walk-Forward" in analysis_type:
            st.subheader("📈 Walk-Forward Settings")
            col1, col2 = st.columns(2)
            with col1:
                window_size = st.slider("Training Window (days)", 20, 60, 30, 5, key="wf_window")
            with col2:
                step_size = st.slider("Test Step (days)", 3, 14, 7, 1, key="wf_step")
            st.info(f"📈 Train on {window_size} days, test on {step_size} days, walk forward")

        # Run backtest button
        st.markdown("---")
        st.subheader("🚀 Run Backtest")

        # Big run button
        if st.button("🚀 **RUN BACKTEST**", type="primary", key="run_backtest"):
            self.run_simple_backtest(
                analysis_type=analysis_type,
                period_type=period_type,
                initial_capital=initial_capital,
                min_confluence=locals().get('min_confluence', 4),
                min_rr=locals().get('min_rr', 4.0),
                max_sl_points=locals().get('max_sl_points', 300),
                window_size=locals().get('window_size', 30),
                step_size=locals().get('step_size', 7),
                days=locals().get('days', 30),
                start_date=locals().get('start_date'),
                end_date=locals().get('end_date')
            )

        # Results display area
        if 'backtest_results' in st.session_state and st.session_state.backtest_results:
            st.markdown("---")
            st.subheader("📊 Backtest Results")
            self.display_simple_results(st.session_state.backtest_results, analysis_type)

    def run_simple_backtest(self, **params):
        """Run simple backtest using existing VectorBT system"""

        try:
            # Use existing dashboard imports system
            from dashboard_imports import BigTrendVectorBTBacktester

            with st.spinner("🔧 Initializing backtest system..."):
                backtester = BigTrendVectorBTBacktester(params['initial_capital'])

            # Load data based on period selection
            with st.spinner("📊 Loading market data..."):
                df_3m, df_15m, df_1h = self.load_backtest_data(params['period_type'], **params)

                if df_3m.empty:
                    st.error("❌ No data available for selected period. Please fetch data first.")
                    return

                st.success(f"✅ Loaded data: {len(df_3m)} 3m candles, {len(df_15m)} 15m candles, {len(df_1h)} 1h candles")

            # Run backtest based on type
            results = None

            if "Basic Backtest" in params['analysis_type']:
                # Run basic backtest
                results = backtester.run_full_backtest(df_3m, df_15m, df_1h, 'basic')

            elif "Parameter Optimization" in params['analysis_type']:
                # Run parameter optimization
                st.info("🔧 Running parameter optimization - this may take a few minutes...")
                results = backtester.run_full_backtest(df_3m, df_15m, df_1h, 'optimization')

            elif "Walk-Forward" in params['analysis_type']:
                # Run walk-forward validation
                st.info("📈 Running walk-forward validation - testing strategy robustness...")
                results = backtester.run_full_backtest(df_3m, df_15m, df_1h, 'basic')
                # Note: Walk-forward would need additional implementation

            # Store results
            if results:
                st.session_state.backtest_results = results
                st.session_state.analysis_type = params['analysis_type']
                st.success("✅ Backtest completed successfully!")
                st.rerun()  # Refresh to show results
            else:
                st.error("❌ Backtest failed")

        except ImportError as e:
            st.error(f"❌ Backtest system not available: {e}")
            st.info("💡 Using fallback mode - basic functionality only")
            # Could implement a simple fallback here
        except Exception as e:
            st.error(f"❌ Backtest failed: {e}")
            import traceback
            with st.expander("🔍 Error Details"):
                st.code(traceback.format_exc())

    def load_backtest_data(self, period_type, **kwargs):
        """Load data for backtesting based on period selection"""

        if period_type == "Recent Days":
            days = kwargs.get('days', 30)
            from datetime import datetime, timedelta
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)

        elif period_type == "Custom Date Range":
            start_date = datetime.combine(kwargs.get('start_date'), datetime.min.time())
            end_date = datetime.combine(kwargs.get('end_date'), datetime.max.time())

        else:  # All Available Data
            start_date = None
            end_date = None

        # Load multi-timeframe data
        df_3m = self.database_manager.get_market_data(
            timeframe='3m',
            start_date=start_date,
            end_date=end_date
        )

        df_15m = self.database_manager.get_market_data(
            timeframe='15m',
            start_date=start_date,
            end_date=end_date
        )

        df_1h = self.database_manager.get_market_data(
            timeframe='1h',
            start_date=start_date,
            end_date=end_date
        )

        return df_3m, df_15m, df_1h

    def display_simple_results(self, results, analysis_type):
        """Display simple, clean backtest results"""

        st.subheader("📊 Backtest Results")

        # Key metrics in a clean layout
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            st.metric("💰 Total Return", f"{results.get('total_return', 0):.2%}")
            st.metric("💵 Final Value", f"${results.get('final_value', 100000):,.2f}")

        with col2:
            st.metric("📈 Sharpe Ratio", f"{results.get('sharpe_ratio', 0):.2f}")
            st.metric("📉 Max Drawdown", f"{results.get('max_drawdown', 0):.2%}")

        with col3:
            st.metric("🎯 Total Trades", results.get('total_trades', 0))
            st.metric("✅ Win Rate", f"{results.get('win_rate', 0):.1%}")

        with col4:
            st.metric("⚡ Profit Factor", f"{results.get('profit_factor', 0):.2f}")
            st.metric("🚀 Big Winners", results.get('big_winners', 0))

        # Performance summary
        st.markdown("---")
        st.subheader("📋 Performance Summary")

        if results.get('total_return', 0) > 0:
            st.success(f"🎉 **Profitable Strategy**: {results.get('total_return', 0):.2%} total return")
        else:
            st.warning(f"⚠️ **Strategy Loss**: {results.get('total_return', 0):.2%} total return")

        # Big trend analysis
        big_winners = results.get('big_winners', 0)
        total_trades = results.get('total_trades', 1)
        if big_winners > 0:
            big_winner_rate = (big_winners / total_trades) * 100
            st.info(f"🚀 **Big Trend Success**: {big_winners} big winners out of {total_trades} trades ({big_winner_rate:.1f}%)")

        # Quick actions
        st.markdown("---")
        col1, col2, col3 = st.columns(3)

        with col1:
            if st.button("🔄 Run Again", key="run_again"):
                st.session_state.backtest_results = None
                st.rerun()

        with col2:
            if st.button("📊 Try Different Period", key="try_different"):
                st.session_state.backtest_results = None
                st.info("💡 Change the data period above and run again")

        with col3:
            if st.button("🔧 Optimize Parameters", key="optimize_now"):
                st.session_state.backtest_results = None
                st.info("💡 Select 'Parameter Optimization' above and run again")

    def display_basic_results(self, results):
        """Display basic backtest results"""

        st.subheader("📊 Basic Backtest Results")

        # Key metrics
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            st.metric("Total Return", f"{results.get('total_return', 0):.2%}")
            st.metric("Final Value", f"${results.get('final_value', 0):,.2f}")

        with col2:
            st.metric("Sharpe Ratio", f"{results.get('sharpe_ratio', 0):.2f}")
            st.metric("Max Drawdown", f"{results.get('max_drawdown', 0):.2%}")

        with col3:
            st.metric("Total Trades", results.get('total_trades', 0))
            st.metric("Win Rate", f"{results.get('win_rate', 0):.1%}")

        with col4:
            st.metric("Profit Factor", f"{results.get('profit_factor', 0):.2f}")
            st.metric("Big Winners", results.get('big_winners', 0))

    def display_optimization_results(self, results):
        """Display parameter optimization results"""

        st.subheader("🔧 Parameter Optimization Results")

        col1, col2 = st.columns(2)

        with col1:
            st.metric("Best Score", f"{results.get('best_score', 0):.4f}")
            st.write("**🏆 Best Parameters:**")
            st.json(results.get('best_params', {}))

        with col2:
            st.write("**📊 Top Results:**")
            top_results = results.get('all_results', [])[:5]
            if top_results:
                df_results = pd.DataFrame(top_results)
                st.dataframe(df_results)

    def display_monte_carlo_results(self, results):
        """Display Monte Carlo analysis results"""

        st.subheader("🎲 Monte Carlo Risk Analysis")

        col1, col2, col3 = st.columns(3)

        with col1:
            st.metric("Expected Return", f"{results.get('expected_return', 0):.2%}")
            st.metric("Return Volatility", f"{results.get('return_volatility', 0):.2%}")

        with col2:
            st.metric("5% VaR", f"{results.get('var_5', 0):.2%}")
            st.metric("1% VaR", f"{results.get('var_1', 0):.2%}")

        with col3:
            st.metric("Probability of Profit", f"{results.get('probability_of_profit', 0):.1%}")
            st.metric("Expected Drawdown", f"{results.get('expected_drawdown', 0):.2%}")

    def display_walk_forward_results(self, results):
        """Display walk-forward analysis results"""

        st.subheader("📈 Walk-Forward Analysis Results")

        summary = results.get('summary', {})

        col1, col2 = st.columns(2)

        with col1:
            st.metric("Average Return", f"{summary.get('avg_return', 0):.2%}")
            st.metric("Average Sharpe", f"{summary.get('avg_sharpe', 0):.2f}")

        with col2:
            st.metric("Periods Tested", summary.get('periods_tested', 0))
            st.metric("Total Trades", summary.get('total_trades', 0))

    def display_position_sizing_results(self, results):
        """Display position sizing comparison results"""

        st.subheader("💰 Position Sizing Comparison")

        if isinstance(results, dict):
            comparison_data = []
            for method, result in results.items():
                comparison_data.append({
                    'Method': method.title(),
                    'Total Return': f"{result.get('total_return', 0):.2%}",
                    'Sharpe Ratio': f"{result.get('sharpe_ratio', 0):.2f}",
                    'Max Drawdown': f"{result.get('max_drawdown', 0):.2%}"
                })

            st.table(pd.DataFrame(comparison_data))

    def display_comprehensive_results(self, results):
        """Display comprehensive analysis results"""

        st.subheader("🚀 Comprehensive Analysis Results")

        # Show summary of all completed analyses
        for analysis_name, result in results.items():
            if result:
                with st.expander(f"✅ {analysis_name.title()} Results"):
                    if analysis_name == 'basic':
                        self.display_basic_results(result)
                    elif analysis_name == 'optimization':
                        self.display_optimization_results(result)
                    elif analysis_name == 'monte_carlo':
                        self.display_monte_carlo_results(result)
                    # Add other result types as needed

        # Note: Removed problematic button sections that had undefined variables

    def run_backtest(self, period_type, params):
        """Run the backtest with given parameters"""

        try:
            # Show progress
            progress_bar = st.progress(0)
            status_text = st.empty()

            status_text.text("🔄 Loading backtesting components...")
            progress_bar.progress(10)

            # Import backtesting components
            from datetime import datetime

            try:
                # Use dashboard-safe imports
                from dashboard.dashboard_imports import get_trading_components
                TradingDataLoader, BigTrendVectorBTBacktester = get_trading_components()
            except ImportError as e:
                st.error(f"❌ Import error: {e}")
                st.error("Make sure all Trading_engine files are present")
                st.error("Check that the crypto_market module is properly installed")
                return

            status_text.text("📊 Loading market data...")
            progress_bar.progress(30)

            # Load data based on period type
            loader = TradingDataLoader()

            if period_type == "Recent Days":
                df_3m, df_15m, df_1h = loader.load_recent_data(days=params['days'])
            elif period_type == "Custom Date Range":
                df_3m, df_15m, df_1h = loader.load_backtest_data(
                    start_date=datetime.combine(params['start_date'], datetime.min.time()),
                    end_date=datetime.combine(params['end_date'], datetime.max.time())
                )
            elif period_type == "Sample":
                df_3m, df_15m, df_1h = loader.load_sample_data(params['sample_size'])
            else:  # All Available Data
                df_3m, df_15m, df_1h = loader.load_backtest_data()

            status_text.text("🎯 Initializing strategy...")
            progress_bar.progress(50)

            # Initialize backtester with custom settings
            backtester = BigTrendVectorBTBacktester(initial_capital=params['initial_capital'])

            # Update strategy settings
            backtester.strategy_settings.update({
                'min_confluence': params['min_confluence'],
                'min_rr': params['min_rr'],
                'max_sl_points': params['max_sl_points']
            })

            status_text.text("🚀 Running backtest...")
            progress_bar.progress(70)

            # Run backtest
            results = backtester.run_full_backtest(df_3m, df_15m, df_1h)

            progress_bar.progress(100)
            status_text.empty()
            progress_bar.empty()

            if results:
                st.session_state.backtest_results = results
                st.session_state.backtest_params = params
                st.success("✅ Backtest completed successfully!")
                st.rerun()
            else:
                st.error("❌ Backtest failed - no signals generated")

        except Exception as e:
            st.error(f"❌ Backtest failed: {e}")
            import traceback
            st.code(traceback.format_exc())

    def display_backtest_results(self):
        """Display comprehensive backtest results"""

        if 'backtest_results' not in st.session_state:
            return

        results = st.session_state.backtest_results
        params = st.session_state.get('backtest_params', {})

        st.markdown("---")
        st.subheader("📊 Backtest Results")

        # Performance overview
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            total_return = results.get('total_return', 0)
            st.metric(
                "💰 Total Return",
                f"{total_return:.2%}",
                delta=f"{total_return:.2%}" if total_return > 0 else None
            )

        with col2:
            win_rate = results.get('win_rate', 0)
            st.metric(
                "🎯 Win Rate",
                f"{win_rate:.1%}",
                delta="Good" if win_rate > 0.5 else "Needs Improvement"
            )

        with col3:
            total_trades = results.get('total_trades', 0)
            st.metric("📈 Total Trades", f"{total_trades}")

        with col4:
            big_winners = results.get('big_winners', 0)
            big_winner_rate = big_winners / total_trades if total_trades > 0 else 0
            st.metric(
                "🚀 Big Winners",
                f"{big_winners}",
                delta=f"{big_winner_rate:.1%} rate"
            )

        # Detailed metrics
        st.markdown("### 📈 Detailed Performance")

        col1, col2 = st.columns(2)

        with col1:
            st.markdown("**💰 Financial Metrics:**")
            st.write(f"• Initial Capital: ${params.get('initial_capital', 100000):,.0f}")
            st.write(f"• Final Value: ${params.get('initial_capital', 100000) * (1 + total_return):,.0f}")
            st.write(f"• Max Drawdown: {results.get('max_drawdown', 0):.2%}")
            st.write(f"• Profit Factor: {results.get('profit_factor', 0):.2f}")
            st.write(f"• Sharpe Ratio: {results.get('sharpe_ratio', 0):.2f}")

        with col2:
            st.markdown("**🎯 Trade Analysis:**")
            st.write(f"• Average Win: ${results.get('avg_win', 0):,.0f}")
            st.write(f"• Average Loss: ${results.get('avg_loss', 0):,.0f}")
            st.write(f"• Best Trade: ${results.get('max_win', 0):,.0f}")
            st.write(f"• Worst Trade: ${results.get('max_loss', 0):,.0f}")
            st.write(f"• Expectancy: ${results.get('expectancy', 0):,.0f}")

        # Big trend analysis
        st.markdown("### 🚀 Big Trend Capture Analysis")

        col1, col2, col3 = st.columns(3)

        with col1:
            st.metric("🎯 Big Winners (800+ pts)", f"{results.get('big_winners', 0)}")

        with col2:
            huge_winners = results.get('huge_winners', 0)
            st.metric("🚀 Huge Winners (2000+ pts)", f"{huge_winners}")

        with col3:
            if total_trades > 0:
                big_winner_percentage = (big_winners / total_trades) * 100
                st.metric("📊 Big Winner Rate", f"{big_winner_percentage:.1f}%")
            else:
                big_winner_percentage = 0
                st.metric("📊 Big Winner Rate", "0.0%")

        # Strategy effectiveness
        if big_winner_percentage > 25:
            st.success("🏆 **EXCELLENT**: Strategy effectively captures big trends!")
        elif big_winner_percentage > 15:
            st.info("✅ **GOOD**: Strategy captures decent big moves")
        else:
            st.warning("⚠️ **NEEDS IMPROVEMENT**: Consider adjusting parameters")

        # Add comprehensive visualizations
        self.display_backtest_charts(results)

    def display_backtest_charts(self, results):
        """Display comprehensive backtest visualization charts"""

        st.markdown("### 📊 Performance Visualization")

        # Get portfolio object for detailed analysis
        pf = results.get('portfolio')
        if pf is None:
            st.warning("⚠️ Portfolio data not available for visualization")
            return

        try:
            # Create tabs for different chart types
            chart_tab1, chart_tab2, chart_tab3, chart_tab4 = st.tabs([
                "📈 Portfolio Performance",
                "💰 Equity Curve",
                "📉 Drawdown Analysis",
                "🎯 Trade Analysis"
            ])

            with chart_tab1:
                self.plot_portfolio_performance(pf)

            with chart_tab2:
                self.plot_equity_curve(pf)

            with chart_tab3:
                self.plot_drawdown_analysis(pf)

            with chart_tab4:
                self.plot_trade_analysis(pf, results)

        except Exception as e:
            st.error(f"❌ Error creating charts: {e}")
            import traceback
            st.code(traceback.format_exc())

    def plot_portfolio_performance(self, pf):
        """Plot portfolio performance overview"""

        st.subheader("📈 Portfolio Performance Overview")

        try:
            # Portfolio value over time
            portfolio_value = pf.value

            fig = go.Figure()
            fig.add_trace(go.Scatter(
                x=portfolio_value.index,
                y=portfolio_value.values,
                mode='lines',
                name='Portfolio Value',
                line=dict(color='#1f77b4', width=2)
            ))

            # Add initial capital line
            initial_value = portfolio_value.iloc[0]
            fig.add_hline(
                y=initial_value,
                line_dash="dash",
                line_color="gray",
                annotation_text="Initial Capital"
            )

            fig.update_layout(
                title="Portfolio Value Over Time",
                xaxis_title="Time",
                yaxis_title="Portfolio Value ($)",
                height=400,
                template="plotly_white"
            )

            st.plotly_chart(fig, use_container_width=True)

            # Performance metrics
            col1, col2, col3 = st.columns(3)
            with col1:
                total_return = pf.total_return()
                st.metric("Total Return", f"{total_return:.2%}")
            with col2:
                final_value = portfolio_value.iloc[-1]
                st.metric("Final Value", f"${final_value:,.2f}")
            with col3:
                profit = final_value - initial_value
                st.metric("Total Profit", f"${profit:,.2f}")

        except Exception as e:
            st.error(f"Error plotting portfolio performance: {e}")

    def plot_equity_curve(self, pf):
        """Plot equity curve with returns"""

        st.subheader("💰 Equity Curve & Returns")

        try:
            # Get returns
            returns = pf.returns()
            cumulative_returns = (1 + returns).cumprod()

            # Create subplot
            fig = make_subplots(
                rows=2, cols=1,
                subplot_titles=('Cumulative Returns', 'Daily Returns'),
                vertical_spacing=0.1,
                row_heights=[0.7, 0.3]
            )

            # Cumulative returns
            fig.add_trace(
                go.Scatter(
                    x=cumulative_returns.index,
                    y=cumulative_returns.values,
                    mode='lines',
                    name='Cumulative Returns',
                    line=dict(color='green', width=2)
                ),
                row=1, col=1
            )

            # Daily returns
            fig.add_trace(
                go.Scatter(
                    x=returns.index,
                    y=returns.values,
                    mode='lines',
                    name='Daily Returns',
                    line=dict(color='blue', width=1)
                ),
                row=2, col=1
            )

            fig.update_layout(
                height=600,
                template="plotly_white",
                showlegend=True
            )

            st.plotly_chart(fig, use_container_width=True)

        except Exception as e:
            st.error(f"Error plotting equity curve: {e}")

    def plot_drawdown_analysis(self, pf):
        """Plot drawdown analysis"""

        st.subheader("📉 Drawdown Analysis")

        try:
            # Get drawdown data
            drawdown = pf.drawdown()

            fig = go.Figure()

            # Plot drawdown
            fig.add_trace(go.Scatter(
                x=drawdown.index,
                y=drawdown.values * 100,  # Convert to percentage
                mode='lines',
                name='Drawdown %',
                fill='tonexty',
                line=dict(color='red', width=1),
                fillcolor='rgba(255, 0, 0, 0.3)'
            ))

            # Add zero line
            fig.add_hline(y=0, line_dash="dash", line_color="gray")

            fig.update_layout(
                title="Portfolio Drawdown Over Time",
                xaxis_title="Time",
                yaxis_title="Drawdown (%)",
                height=400,
                template="plotly_white"
            )

            st.plotly_chart(fig, use_container_width=True)

            # Drawdown statistics
            max_dd = pf.max_drawdown()
            avg_dd = drawdown.mean()

            col1, col2 = st.columns(2)
            with col1:
                st.metric("Max Drawdown", f"{max_dd:.2%}")
            with col2:
                st.metric("Average Drawdown", f"{avg_dd:.2%}")

        except Exception as e:
            st.error(f"Error plotting drawdown: {e}")

    def plot_trade_analysis(self, pf, results):
        """Plot trade analysis charts"""

        st.subheader("🎯 Trade Analysis")

        # Use results parameter to avoid warning
        _ = results

        try:
            # Check if we have trades
            if len(pf.trades.records) == 0:
                st.info("No trades to analyze")
                return

            # Get trade data
            trades_df = pf.trades.records_readable

            # Trade PnL distribution
            fig = go.Figure()

            # Histogram of trade PnL
            fig.add_trace(go.Histogram(
                x=trades_df['PnL'],
                nbinsx=20,
                name='Trade PnL Distribution',
                marker_color='lightblue',
                opacity=0.7
            ))

            fig.update_layout(
                title="Trade PnL Distribution",
                xaxis_title="PnL ($)",
                yaxis_title="Number of Trades",
                height=400,
                template="plotly_white"
            )

            st.plotly_chart(fig, use_container_width=True)

            # Trade statistics table
            st.markdown("**📊 Trade Statistics:**")

            trade_stats = {
                'Metric': [
                    'Total Trades',
                    'Winning Trades',
                    'Losing Trades',
                    'Win Rate',
                    'Average Win',
                    'Average Loss',
                    'Best Trade',
                    'Worst Trade',
                    'Profit Factor'
                ],
                'Value': [
                    f"{len(trades_df)}",
                    f"{len(trades_df[trades_df['PnL'] > 0])}",
                    f"{len(trades_df[trades_df['PnL'] < 0])}",
                    f"{pf.trades.win_rate():.1%}",
                    f"${trades_df[trades_df['PnL'] > 0]['PnL'].mean():.2f}" if len(trades_df[trades_df['PnL'] > 0]) > 0 else "$0.00",
                    f"${trades_df[trades_df['PnL'] < 0]['PnL'].mean():.2f}" if len(trades_df[trades_df['PnL'] < 0]) > 0 else "$0.00",
                    f"${trades_df['PnL'].max():.2f}",
                    f"${trades_df['PnL'].min():.2f}",
                    f"{pf.trades.profit_factor():.2f}"
                ]
            }

            st.dataframe(pd.DataFrame(trade_stats), use_container_width=True)

        except Exception as e:
            st.error(f"Error plotting trade analysis: {e}")

    def render_signal_analysis_page(self):
        """Simple signal analysis"""
        st.header("🎯 Signal Analysis")
        st.info("🚧 Signal analysis features coming soon!")

    def run(self):
        """Run the dashboard"""
        page = self.render_sidebar()

        if page == "📊 Dashboard":
            self.render_dashboard_page()
        elif page == "📈 Data Management":
            self.render_data_management_page()
        elif page == "🚀 Backtesting":
            self.render_backtesting_page()
        elif page == "🎯 Signal Analysis":
            self.render_signal_analysis_page()

# Run the dashboard
if __name__ == "__main__":
    dashboard = SimpleTradingDashboard()
    dashboard.run()
