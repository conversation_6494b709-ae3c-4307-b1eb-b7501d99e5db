"""
Trading System Enums
====================

Common enums used across the trading system.
"""

from enum import Enum

class SignalStrength(Enum):
    """Signal strength classification"""
    WEAK = "weak"
    MODERATE = "moderate"
    STRONG = "strong"
    ULTRA_HIGH = "ultra_high"

class TrailingMethod(Enum):
    """Trailing stop methods"""
    ATR_BASED = "atr_based"
    STRUCTURE_BASED = "structure_based"
    DYNAMIC = "dynamic"
    EMA_BASED = "ema_based"

class TradingSignal(Enum):
    """Trading signal types"""
    BUY = "BUY"
    SELL = "SELL"
    HOLD = "HOLD"
