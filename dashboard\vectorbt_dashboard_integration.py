"""
VectorBT Dashboard Integration - Full Potential
==============================================

Complete dashboard integration for VectorBT's full potential features.
This module provides a clean interface between the dashboard and advanced VectorBT capabilities.
"""

import sys
import os
from pathlib import Path
import pandas as pd
import numpy as np
import streamlit as st
from datetime import datetime, timedelta

# Setup paths
dashboard_dir = Path(__file__).parent.absolute()
agent_trading_dir = dashboard_dir.parent.absolute()
trading_engine_dir = agent_trading_dir / "crypto_market" / "engines" / "Trading_engine"

# Add paths for imports
sys.path.insert(0, str(agent_trading_dir))
sys.path.insert(0, str(trading_engine_dir))

class VectorBTDashboardManager:
    """
    Dashboard manager for VectorBT full potential features
    
    This class provides a clean interface for the dashboard to access
    all advanced VectorBT capabilities with proper error handling.
    """
    
    def __init__(self, initial_capital=100000):
        self.initial_capital = initial_capital
        self.backtester = None
        self.results = {}
        self.visualization_data = {}
        
        # Initialize VectorBT system
        self._initialize_vectorbt_system()
    
    def _initialize_vectorbt_system(self):
        """Initialize the VectorBT system with proper error handling"""
        try:
            # Import enhanced VectorBT system
            from backtester_vbt import (
                BigTrendVectorBTBacktester,
                run_comprehensive_analysis,
                run_big_trend_backtest
            )
            
            self.BigTrendVectorBTBacktester = BigTrendVectorBTBacktester
            self.run_comprehensive_analysis = run_comprehensive_analysis
            self.run_big_trend_backtest = run_big_trend_backtest
            
            # Initialize backtester
            self.backtester = BigTrendVectorBTBacktester(self.initial_capital)
            
            st.success("✅ VectorBT Full Potential System Initialized")
            return True
            
        except ImportError as e:
            st.error(f"❌ VectorBT system import failed: {e}")
            st.warning("🔄 Using fallback mode - limited features available")
            return False
        except Exception as e:
            st.error(f"❌ VectorBT initialization failed: {e}")
            return False
    
    def run_basic_backtest(self, df_3m, df_15m, df_1h):
        """Run basic enhanced backtest"""
        if not self.backtester:
            st.error("❌ VectorBT system not available")
            return None
        
        with st.spinner("🚀 Running Enhanced Basic Backtest..."):
            try:
                results = self.run_big_trend_backtest(
                    df_3m, df_15m, df_1h, 
                    self.initial_capital, 
                    'basic'
                )
                
                if results:
                    self.results['basic'] = results
                    st.success("✅ Basic backtest completed!")
                    return results
                else:
                    st.error("❌ Basic backtest failed")
                    return None
                    
            except Exception as e:
                st.error(f"❌ Basic backtest error: {e}")
                return None
    
    def run_parameter_optimization(self, df_3m, df_15m, df_1h, param_ranges=None):
        """
        Run parameter optimization with dashboard integration
        
        This finds the best parameters for your strategy automatically.
        """
        if not self.backtester:
            st.error("❌ VectorBT system not available")
            return None
        
        st.info("🔧 **Parameter Optimization** finds the best settings for your strategy")
        st.write("This tests different combinations of parameters to maximize performance.")
        
        if param_ranges is None:
            param_ranges = {
                'min_confluence': [3, 4, 5],
                'min_rr': [3.0, 4.0, 5.0],
                'max_sl_points': [250, 300, 350],
                'atr_sl_multiplier': [1.0, 1.5, 2.0]
            }
        
        # Show what will be tested
        st.write("**Parameters to optimize:**")
        for param, values in param_ranges.items():
            st.write(f"- {param}: {values}")
        
        total_combinations = np.prod([len(v) for v in param_ranges.values()])
        st.write(f"**Total combinations to test:** {total_combinations}")
        
        with st.spinner(f"🔧 Testing {total_combinations} parameter combinations..."):
            try:
                results = self.backtester.run_parameter_optimization(
                    df_3m, df_15m, df_1h,
                    param_ranges=param_ranges,
                    optimization_metric='sharpe_ratio'
                )
                
                if results:
                    self.results['optimization'] = results
                    st.success("✅ Parameter optimization completed!")
                    
                    # Display results
                    st.write("**🏆 Best Parameters Found:**")
                    st.json(results['best_params'])
                    st.metric("Best Sharpe Ratio", f"{results['best_score']:.4f}")
                    
                    return results
                else:
                    st.error("❌ Parameter optimization failed")
                    return None
                    
            except Exception as e:
                st.error(f"❌ Parameter optimization error: {e}")
                return None
    
    def run_monte_carlo_analysis(self, df_3m, df_15m, df_1h, num_simulations=500):
        """
        Run Monte Carlo simulation with dashboard integration
        
        This analyzes risk by running thousands of simulations.
        """
        if not self.backtester:
            st.error("❌ VectorBT system not available")
            return None
        
        st.info("🎲 **Monte Carlo Analysis** simulates thousands of possible outcomes")
        st.write("This helps understand the risk profile and probability of different returns.")
        
        with st.spinner(f"🎲 Running {num_simulations} Monte Carlo simulations..."):
            try:
                results = self.backtester.run_monte_carlo_simulation(
                    df_3m, df_15m, df_1h,
                    num_simulations=num_simulations
                )
                
                if results:
                    self.results['monte_carlo'] = results
                    st.success("✅ Monte Carlo analysis completed!")
                    
                    # Display key risk metrics
                    col1, col2, col3 = st.columns(3)
                    
                    with col1:
                        st.metric("Expected Return", f"{results['expected_return']:.2%}")
                        st.metric("5% VaR", f"{results['var_5']:.2%}")
                    
                    with col2:
                        st.metric("Probability of Profit", f"{results['probability_of_profit']:.1%}")
                        st.metric("Expected Drawdown", f"{results['expected_drawdown']:.2%}")
                    
                    with col3:
                        st.metric("Return Volatility", f"{results['return_volatility']:.2%}")
                        st.metric("Worst Drawdown", f"{results['worst_drawdown']:.2%}")
                    
                    return results
                else:
                    st.error("❌ Monte Carlo analysis failed")
                    return None
                    
            except Exception as e:
                st.error(f"❌ Monte Carlo analysis error: {e}")
                return None
    
    def run_walk_forward_analysis(self, df_3m, df_15m, df_1h, window_size=30, step_size=7):
        """
        Run walk-forward analysis with dashboard integration
        
        This validates strategy robustness using out-of-sample testing.
        """
        if not self.backtester:
            st.error("❌ VectorBT system not available")
            return None
        
        st.info("📈 **Walk-Forward Analysis** tests strategy on unseen future data")
        st.write("This validates that your strategy works consistently over time.")
        
        # Show analysis parameters
        st.write(f"**Training window:** {window_size} days")
        st.write(f"**Testing step:** {step_size} days")
        
        total_days = (df_3m.index[-1] - df_3m.index[0]).days
        estimated_periods = max(1, (total_days - window_size) // step_size)
        st.write(f"**Estimated test periods:** {estimated_periods}")
        
        with st.spinner("📈 Running walk-forward analysis..."):
            try:
                results = self.backtester.run_walk_forward_analysis(
                    df_3m, df_15m, df_1h,
                    window_size=window_size,
                    step_size=step_size
                )
                
                if results:
                    self.results['walk_forward'] = results
                    st.success("✅ Walk-forward analysis completed!")
                    
                    # Display summary results
                    summary = results['summary']
                    
                    col1, col2 = st.columns(2)
                    with col1:
                        st.metric("Average Return", f"{summary['avg_return']:.2%}")
                        st.metric("Average Sharpe", f"{summary['avg_sharpe']:.2f}")
                    
                    with col2:
                        st.metric("Periods Tested", summary['periods_tested'])
                        st.metric("Total Trades", summary['total_trades'])
                    
                    return results
                else:
                    st.error("❌ Walk-forward analysis failed")
                    return None
                    
            except Exception as e:
                st.error(f"❌ Walk-forward analysis error: {e}")
                return None
    
    def run_dynamic_position_sizing(self, df_3m, df_15m, df_1h, sizing_methods=None):
        """
        Test different position sizing methods
        
        This optimizes position sizes for maximum returns with proper risk control.
        """
        if not self.backtester:
            st.error("❌ VectorBT system not available")
            return None
        
        if sizing_methods is None:
            sizing_methods = ['kelly', 'volatility', 'fixed_risk', 'adaptive']
        
        st.info("💰 **Dynamic Position Sizing** optimizes trade sizes for maximum returns")
        st.write("This tests different methods to determine optimal position sizes.")
        
        results = {}
        
        for method in sizing_methods:
            with st.spinner(f"💰 Testing {method} position sizing..."):
                try:
                    result = self.backtester.run_dynamic_position_sizing(
                        df_3m, df_15m, df_1h,
                        sizing_method=method,
                        risk_target=0.02
                    )
                    
                    if result:
                        results[method] = result
                        st.success(f"✅ {method.title()} sizing: {result.get('total_return', 0):.2%} return")
                    else:
                        st.warning(f"⚠️ {method.title()} sizing failed")
                        
                except Exception as e:
                    st.error(f"❌ {method.title()} sizing error: {e}")
        
        if results:
            self.results['position_sizing'] = results
            
            # Display comparison
            st.write("**📊 Position Sizing Comparison:**")
            comparison_data = []
            for method, result in results.items():
                comparison_data.append({
                    'Method': method.title(),
                    'Total Return': f"{result.get('total_return', 0):.2%}",
                    'Sharpe Ratio': f"{result.get('sharpe_ratio', 0):.2f}",
                    'Max Drawdown': f"{result.get('max_drawdown', 0):.2%}"
                })
            
            st.table(pd.DataFrame(comparison_data))
            
            return results
        else:
            st.error("❌ All position sizing methods failed")
            return None
    
    def run_comprehensive_analysis(self, df_3m, df_15m, df_1h):
        """
        Run comprehensive analysis with ALL VectorBT features
        
        This is the ultimate analysis using VectorBT's full potential.
        """
        if not self.backtester:
            st.error("❌ VectorBT system not available")
            return None
        
        st.info("🚀 **Comprehensive Analysis** - VectorBT's FULL POTENTIAL")
        st.write("This runs ALL advanced features for complete strategy analysis.")
        
        with st.spinner("🚀 Running comprehensive analysis..."):
            try:
                results = self.run_comprehensive_analysis(
                    df_3m, df_15m, df_1h,
                    self.initial_capital,
                    'full'
                )
                
                if results:
                    self.results['comprehensive'] = results
                    st.success("✅ Comprehensive analysis completed!")
                    
                    # Display summary of all analyses
                    st.write("**📋 Analysis Summary:**")
                    for analysis_name, result in results.items():
                        if result:
                            st.write(f"✅ {analysis_name.title()}: Completed")
                        else:
                            st.write(f"❌ {analysis_name.title()}: Failed")
                    
                    return results
                else:
                    st.error("❌ Comprehensive analysis failed")
                    return None
                    
            except Exception as e:
                st.error(f"❌ Comprehensive analysis error: {e}")
                return None
    
    def get_results_summary(self):
        """Get summary of all completed analyses"""
        if not self.results:
            return "No analyses completed yet."
        
        summary = "## 📊 Analysis Results Summary\n\n"
        
        for analysis_name, result in self.results.items():
            if result:
                summary += f"### ✅ {analysis_name.title()}\n"
                
                if analysis_name == 'basic':
                    summary += f"- Total Return: {result.get('total_return', 0):.2%}\n"
                    summary += f"- Sharpe Ratio: {result.get('sharpe_ratio', 0):.2f}\n"
                    summary += f"- Max Drawdown: {result.get('max_drawdown', 0):.2%}\n"
                    summary += f"- Total Trades: {result.get('total_trades', 0)}\n"
                    summary += f"- Win Rate: {result.get('win_rate', 0):.1%}\n\n"
                
                elif analysis_name == 'optimization':
                    summary += f"- Best Score: {result.get('best_score', 0):.4f}\n"
                    summary += f"- Best Parameters: {result.get('best_params', {})}\n\n"
                
                elif analysis_name == 'monte_carlo':
                    summary += f"- Expected Return: {result.get('expected_return', 0):.2%}\n"
                    summary += f"- 5% VaR: {result.get('var_5', 0):.2%}\n"
                    summary += f"- Probability of Profit: {result.get('probability_of_profit', 0):.1%}\n\n"
        
        return summary
