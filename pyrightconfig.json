{"include": ["dashboard", "crypto_market", "core"], "extraPaths": ["./crypto_market/engines/Trading_engine", "./crypto_market/engines", "./crypto_market", "./core", "./dashboard"], "pythonVersion": "3.11", "pythonPlatform": "Windows", "typeCheckingMode": "basic", "reportMissingImports": "warning", "reportMissingTypeStubs": "none", "reportImportCycles": "none", "reportUnusedImport": "none", "reportUnusedVariable": "none", "reportDuplicateImport": "none", "reportOptionalSubscript": "none", "reportOptionalMemberAccess": "none", "reportOptionalCall": "none", "reportOptionalIterable": "none", "reportOptionalContextManager": "none", "reportOptionalOperand": "none", "reportTypedDictNotRequiredAccess": "none", "reportPrivateUsage": "none", "reportConstantRedefinition": "none", "reportIncompatibleMethodOverride": "none", "reportIncompatibleVariableOverride": "none", "reportOverlappingOverloads": "none"}